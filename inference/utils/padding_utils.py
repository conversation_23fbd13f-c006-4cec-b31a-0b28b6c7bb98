# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import torch
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.bert_padding import unpad_input, pad_input

logger = logging.getLogger(__name__)


class PaddingUtils:
    """
    Utilities for handling padding and unpadding operations during inference.
    
    Provides a unified interface for converting between padded and unpadded formats
    while maintaining compatibility with the training configuration.
    """
    
    @staticmethod
    def unpad_sequences(
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, int]:
        """
        Unpad input sequences for unpadded attention.
        
        Args:
            input_ids: Padded input token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            
        Returns:
            Tuple of (unpadded_ids, indices, cu_seqlens, max_seqlen)
        """
        # Add feature dimension for unpad_input compatibility
        input_ids_expanded = input_ids.unsqueeze(-1)
        
        # Unpad the sequences
        unpadded_ids, indices, cu_seqlens, max_seqlen = unpad_input(
            input_ids_expanded, attention_mask
        )
        
        # Remove the added feature dimension
        unpadded_ids = unpadded_ids.squeeze(-1)
        
        logger.debug(f"Unpadded sequences: {input_ids.shape} -> {unpadded_ids.shape}")
        return unpadded_ids, indices, cu_seqlens, max_seqlen
    
    @staticmethod
    def pad_sequences(
        unpadded_tensor: torch.Tensor,
        indices: torch.Tensor,
        batch_size: int,
        seq_len: int
    ) -> torch.Tensor:
        """
        Pad unpadded sequences back to original shape.
        
        Args:
            unpadded_tensor: Unpadded tensor [total_tokens, ...]
            indices: Indices for padding
            batch_size: Original batch size
            seq_len: Original sequence length
            
        Returns:
            Padded tensor [batch_size, seq_len, ...]
        """
        padded_tensor = pad_input(unpadded_tensor, indices, batch_size, seq_len)
        
        logger.debug(f"Padded sequences: {unpadded_tensor.shape} -> {padded_tensor.shape}")
        return padded_tensor
    
    @staticmethod
    def get_sequence_lengths(cu_seqlens: torch.Tensor) -> torch.Tensor:
        """
        Get individual sequence lengths from cumulative sequence lengths.
        
        Args:
            cu_seqlens: Cumulative sequence lengths [batch_size + 1]
            
        Returns:
            Sequence lengths [batch_size]
        """
        return cu_seqlens[1:] - cu_seqlens[:-1]
    
    @staticmethod
    def create_attention_mask_from_cu_seqlens(
        cu_seqlens: torch.Tensor,
        max_seqlen: int,
        device: torch.device
    ) -> torch.Tensor:
        """
        Create attention mask from cumulative sequence lengths.
        
        Args:
            cu_seqlens: Cumulative sequence lengths
            max_seqlen: Maximum sequence length
            device: Target device
            
        Returns:
            Attention mask [batch_size, max_seqlen]
        """
        batch_size = cu_seqlens.shape[0] - 1
        attention_mask = torch.zeros(batch_size, max_seqlen, device=device, dtype=torch.bool)
        
        for i in range(batch_size):
            seq_len = cu_seqlens[i + 1] - cu_seqlens[i]
            attention_mask[i, :seq_len] = True
        
        return attention_mask
    
    @staticmethod
    def validate_unpadded_inputs(
        input_ids: torch.Tensor,
        indices: torch.Tensor,
        cu_seqlens: torch.Tensor,
        max_seqlen: int
    ) -> bool:
        """
        Validate unpadded input tensors for consistency.
        
        Args:
            input_ids: Unpadded input IDs
            indices: Unpadding indices
            cu_seqlens: Cumulative sequence lengths
            max_seqlen: Maximum sequence length
            
        Returns:
            True if inputs are valid
        """
        # Check tensor shapes
        if input_ids.shape[0] != indices.shape[0]:
            logger.error(f"Mismatch in unpadded shapes: {input_ids.shape[0]} vs {indices.shape[0]}")
            return False
        
        # Check cumulative sequence lengths
        total_tokens = cu_seqlens[-1].item()
        if total_tokens != input_ids.shape[0]:
            logger.error(f"Total tokens mismatch: {total_tokens} vs {input_ids.shape[0]}")
            return False
        
        # Check maximum sequence length
        seq_lengths = PaddingUtils.get_sequence_lengths(cu_seqlens)
        actual_max_seqlen = seq_lengths.max().item()
        if actual_max_seqlen != max_seqlen:
            logger.warning(f"Max seqlen mismatch: {actual_max_seqlen} vs {max_seqlen}")
        
        logger.debug("Unpadded inputs validation passed")
        return True
    
    @staticmethod
    def batch_unpad_sequences(
        input_ids_list: List[torch.Tensor],
        attention_mask_list: List[torch.Tensor]
    ) -> Tuple[torch.Tensor, List[torch.Tensor], List[torch.Tensor], List[int]]:
        """
        Unpad multiple batches of sequences.
        
        Args:
            input_ids_list: List of padded input ID tensors
            attention_mask_list: List of attention mask tensors
            
        Returns:
            Tuple of (concatenated_unpadded_ids, indices_list, cu_seqlens_list, max_seqlen_list)
        """
        unpadded_ids_list = []
        indices_list = []
        cu_seqlens_list = []
        max_seqlen_list = []
        
        for input_ids, attention_mask in zip(input_ids_list, attention_mask_list):
            unpadded_ids, indices, cu_seqlens, max_seqlen = PaddingUtils.unpad_sequences(
                input_ids, attention_mask
            )
            unpadded_ids_list.append(unpadded_ids)
            indices_list.append(indices)
            cu_seqlens_list.append(cu_seqlens)
            max_seqlen_list.append(max_seqlen)
        
        # Concatenate all unpadded sequences
        concatenated_unpadded_ids = torch.cat(unpadded_ids_list, dim=0)
        
        logger.debug(f"Batch unpadded {len(input_ids_list)} sequences")
        return concatenated_unpadded_ids, indices_list, cu_seqlens_list, max_seqlen_list
    
    @staticmethod
    def compute_unpadded_embeddings_pooling(
        hidden_states: torch.Tensor,
        cu_seqlens: torch.Tensor,
        pooling_strategy: str = "mean"
    ) -> torch.Tensor:
        """
        Pool unpadded hidden states to get sequence-level embeddings.
        
        Args:
            hidden_states: Unpadded hidden states [total_tokens, hidden_size]
            cu_seqlens: Cumulative sequence lengths
            pooling_strategy: Pooling strategy ("mean", "cls", "max")
            
        Returns:
            Pooled embeddings [batch_size, hidden_size]
        """
        batch_size = cu_seqlens.shape[0] - 1
        hidden_size = hidden_states.shape[-1]
        embeddings = torch.zeros(batch_size, hidden_size, device=hidden_states.device, dtype=hidden_states.dtype)
        
        for i in range(batch_size):
            start_idx = cu_seqlens[i].item()
            end_idx = cu_seqlens[i + 1].item()
            
            if start_idx == end_idx:
                # Empty sequence
                continue
            
            seq_hidden = hidden_states[start_idx:end_idx]
            
            if pooling_strategy == "mean":
                embeddings[i] = seq_hidden.mean(dim=0)
            elif pooling_strategy == "cls":
                # Assume first token is CLS
                embeddings[i] = seq_hidden[0]
            elif pooling_strategy == "max":
                embeddings[i] = seq_hidden.max(dim=0)[0]
            elif pooling_strategy == "mean_sqrt_len":
                # Mean pooling normalized by sqrt of sequence length
                seq_len = end_idx - start_idx
                embeddings[i] = seq_hidden.mean(dim=0) * torch.sqrt(torch.tensor(seq_len, dtype=torch.float))
            else:
                raise ValueError(f"Unknown pooling strategy: {pooling_strategy}")
        
        logger.debug(f"Pooled unpadded embeddings with strategy: {pooling_strategy}")
        return embeddings 