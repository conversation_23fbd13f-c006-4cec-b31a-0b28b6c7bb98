#!/usr/bin/env python3
"""
Final Diagnostic Script

This script performs a deep dive to understand why the model is performing so poorly
compared to the reported 80% training accuracy.
"""

import sys
import os
from pathlib import Path
import torch
import numpy as np
from transformers import AutoTokenizer, DataCollatorForLanguageModeling

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

from inference import ModernBERTInference

def final_diagnostic():
    """Perform comprehensive diagnostic to understand the accuracy discrepancy."""
    
    print("🔬 Final Diagnostic Analysis")
    print("=" * 60)
    
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited"
    
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    
    print("🧪 Test 1: Simple sanity check with known examples")
    test_simple_examples(tokenizer)
    
    print("\n🧪 Test 2: Check if model predicts anything reasonable")
    test_model_predictions(config_path, checkpoint_path, tokenizer)
    
    print("\n🧪 Test 3: Compare training vs inference text processing")
    test_text_processing_differences(tokenizer)
    
    print("\n🧪 Test 4: Check model's raw prediction capabilities")
    test_raw_model_capabilities(config_path, checkpoint_path, tokenizer)

def test_simple_examples(tokenizer):
    """Test with very simple, obvious examples."""
    
    simple_tests = [
        ("My name is <mask>", ["John", "Mary", "Alice"]),
        ("The color of the sky is <mask>", ["blue", "clear", "bright"]),
        ("Two plus two equals <mask>", ["four", "4", "2"]),
        ("The capital of France is <mask>", ["Paris", "Lyon", "Nice"]),
    ]
    
    for text, expected_words in simple_tests:
        print(f"  Text: {text}")
        print(f"  Expected words: {expected_words}")
        
        # Check tokenization
        tokens = tokenizer.tokenize(text)
        token_ids = tokenizer.encode(text)
        print(f"  Tokens: {tokens}")
        print(f"  Token IDs: {token_ids}")
        
        # Find mask position
        mask_pos = [i for i, tid in enumerate(token_ids) if tid == tokenizer.mask_token_id]
        print(f"  Mask positions: {mask_pos}")
        print()

def test_model_predictions(config_path, checkpoint_path, tokenizer):
    """Test what the model actually predicts for simple cases."""
    
    with ModernBERTInference(config_path, checkpoint_path) as inference:
        simple_texts = [
            "The capital of France is <mask>.",
            "My name is <mask>.",
            "The sky is <mask>.",
            "I like to eat <mask>."
        ]
        
        for text in simple_texts:
            print(f"  Testing: {text}")
            
            try:
                predictions = inference.predict_masked_tokens(text, top_k=10)
                
                if predictions and 'mask_predictions' in predictions:
                    print(f"    Top 10 predictions:")
                    for mask_pred in predictions['mask_predictions']:
                        for i, pred in enumerate(mask_pred['predictions'][:10]):
                            print(f"      {i+1}. '{pred['token']}' (prob: {pred['probability']:.3f})")
                else:
                    print(f"    No predictions returned")
                    
            except Exception as e:
                print(f"    Error: {e}")
            print()

def test_text_processing_differences(tokenizer):
    """Compare how text is processed in training vs inference."""
    
    text = "The capital of France is Paris."
    print(f"  Original text: {text}")
    
    # Training-style processing
    collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=True,
        mlm_probability=0.15
    )
    
    encoded = tokenizer(
        text,
        truncation=True,
        max_length=1024,
        padding='max_length',
        return_tensors='pt'
    )
    
    batch = collator([{
        'input_ids': encoded['input_ids'].squeeze(),
        'attention_mask': encoded['attention_mask'].squeeze()
    }])
    
    input_ids = batch['input_ids'].squeeze()
    labels = batch['labels'].squeeze()
    
    print(f"  Training processing:")
    print(f"    Input IDs shape: {input_ids.shape}")
    print(f"    Labels shape: {labels.shape}")
    
    # Find masked positions
    masked_positions = (labels != -100).nonzero(as_tuple=False).flatten()
    print(f"    Masked positions: {masked_positions.tolist()}")
    
    # Show what got masked
    for pos in masked_positions[:5]:
        original_token_id = labels[pos].item()
        masked_token_id = input_ids[pos].item()
        
        original_token = tokenizer.decode([original_token_id])
        if masked_token_id == tokenizer.mask_token_id:
            masked_token = "<mask>"
        else:
            masked_token = tokenizer.decode([masked_token_id])
        
        print(f"      Pos {pos}: '{original_token}' -> '{masked_token}'")
    
    # Inference-style processing
    print(f"\n  Inference processing:")
    inference_text = "The capital of France is <mask>."
    inference_encoded = tokenizer.encode(inference_text)
    print(f"    Inference text: {inference_text}")
    print(f"    Inference token IDs: {inference_encoded}")
    
    # Compare mask positions
    inference_mask_pos = [i for i, tid in enumerate(inference_encoded) if tid == tokenizer.mask_token_id]
    print(f"    Inference mask positions: {inference_mask_pos}")

def test_raw_model_capabilities(config_path, checkpoint_path, tokenizer):
    """Test the model's raw prediction capabilities without our inference pipeline."""
    
    print("  Testing raw model capabilities...")
    
    with ModernBERTInference(config_path, checkpoint_path) as inference:
        # Get the raw model
        mlm_pipeline = inference._get_mlm_pipeline()
        model = mlm_pipeline.model
        
        # Simple test: predict a single masked token
        text = "The capital of France is <mask>."
        
        # Tokenize
        encoded = tokenizer(text, return_tensors='pt')
        input_ids = encoded['input_ids']
        attention_mask = encoded['attention_mask']
        
        print(f"    Input: {text}")
        print(f"    Input IDs: {input_ids}")
        print(f"    Attention mask: {attention_mask}")
        
        # Find mask position
        mask_positions = (input_ids == tokenizer.mask_token_id).nonzero(as_tuple=False)
        print(f"    Mask positions: {mask_positions}")
        
        if len(mask_positions) > 0:
            # Prepare inputs for unpadded model
            if mlm_pipeline.model_config.is_unpadded:
                from src.bert_padding import unpad_input
                unpadded_ids, indices, cu_seqlens, max_seqlen = unpad_input(
                    input_ids.unsqueeze(-1), attention_mask
                )
                unpadded_ids = unpadded_ids.squeeze(-1)
                
                model_inputs = {
                    "input_ids": unpadded_ids.to(model.device),
                    "cu_seqlens": cu_seqlens.to(model.device),
                    "max_seqlen": max_seqlen
                }
                
                print(f"    Unpadded input IDs shape: {unpadded_ids.shape}")
                print(f"    cu_seqlens: {cu_seqlens}")
                print(f"    max_seqlen: {max_seqlen}")
            else:
                model_inputs = {
                    "input_ids": input_ids.to(model.device),
                    "attention_mask": attention_mask.to(model.device)
                }
            
            # Run model
            model.eval()
            with torch.no_grad():
                outputs = model(**model_inputs)
                logits = outputs.logits
                
                print(f"    Output logits shape: {logits.shape}")
                
                # Find mask position in unpadded sequence
                if mlm_pipeline.model_config.is_unpadded:
                    mask_idx = (unpadded_ids == tokenizer.mask_token_id).nonzero(as_tuple=False)
                    if len(mask_idx) > 0:
                        mask_idx = mask_idx[0, 0].item()
                        mask_logits = logits[mask_idx]
                        
                        # Get top predictions
                        top_logits, top_indices = torch.topk(mask_logits, 10)
                        
                        print(f"    Top 10 raw predictions:")
                        for i, (logit, token_id) in enumerate(zip(top_logits, top_indices)):
                            token_text = tokenizer.decode([token_id.item()])
                            prob = torch.softmax(mask_logits, dim=-1)[token_id].item()
                            print(f"      {i+1}. '{token_text}' (logit: {logit:.3f}, prob: {prob:.3f})")
                        
                        # Check if Paris is in top predictions
                        paris_token_id = tokenizer.encode("Paris", add_special_tokens=False)[0]
                        paris_logit = mask_logits[paris_token_id].item()
                        paris_rank = (mask_logits > paris_logit).sum().item() + 1
                        print(f"    Paris rank: {paris_rank} (logit: {paris_logit:.3f})")

if __name__ == "__main__":
    final_diagnostic()
