# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
import torch
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.bert_layers.model import FlexBertForMaskedLM

# Handle imports - try relative first, then absolute
try:
    from ..config.model_config import ModelConfig
    from ..config.inference_config import InferenceConfig
    from ..core.model_factory import ModelFactory
    from ..core.input_processor import InputProcessor
    from ..utils.validation_utils import ValidationUtils
except ImportError:
    # If relative imports fail, try direct module imports
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))
    
    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig
    from core.model_factory import ModelFactory
    from core.input_processor import InputProcessor
    from utils.validation_utils import ValidationUtils

logger = logging.getLogger(__name__)


class BasePipeline(ABC):
    """
    Base class for all inference pipelines.
    
    Provides common functionality for model management, input processing,
    and device handling across different inference tasks.
    """
    
    def __init__(
        self,
        model_config: ModelConfig,
        inference_config: InferenceConfig,
        checkpoint_path: Optional[str] = None
    ):
        """
        Initialize base pipeline.
        
        Args:
            model_config: Model configuration
            inference_config: Inference configuration  
            checkpoint_path: Optional path to model checkpoint
        """
        self.model_config = model_config
        self.inference_config = inference_config
        self.checkpoint_path = checkpoint_path
        
        # Initialize components
        self.model: Optional[FlexBertForMaskedLM] = None
        self.input_processor: Optional[InputProcessor] = None
        self.model_factory: Optional[ModelFactory] = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        logger.info(f"Initializing {self.__class__.__name__}")
        
        # Initialize pipeline
        self._initialize()
    
    def _initialize(self):
        """Initialize pipeline components."""
        try:
            # Create model factory
            self.model_factory = ModelFactory(self.model_config, self.inference_config)
            
            # Create input processor
            self.input_processor = InputProcessor(self.model_config, self.inference_config)
            
            # Create and load model
            self.model = self.model_factory.create_model(self.checkpoint_path)
            
            logger.info(f"{self.__class__.__name__} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.__class__.__name__}: {e}")
            raise
    
    @abstractmethod
    def predict(self, inputs: Union[str, List[str]], **kwargs) -> Any:
        """
        Abstract method for making predictions.
        
        Args:
            inputs: Input text(s) for inference
            **kwargs: Additional parameters specific to the pipeline
            
        Returns:
            Prediction results
        """
        pass
    
    def batch_predict(
        self, 
        inputs: List[str], 
        batch_size: Optional[int] = None,
        **kwargs
    ) -> List[Any]:
        """
        Perform batched prediction for efficiency.
        
        Args:
            inputs: List of input texts
            batch_size: Batch size for processing (uses config default if None)
            **kwargs: Additional parameters
            
        Returns:
            List of prediction results
        """
        if not inputs:
            return []
        
        batch_size = batch_size or self.inference_config.max_batch_size
        results = []
        
        for i in range(0, len(inputs), batch_size):
            batch = inputs[i:i + batch_size]
            batch_results = self.predict(batch, **kwargs)
            
            # Handle single vs batch results
            if isinstance(batch_results, list):
                results.extend(batch_results)
            else:
                results.append(batch_results)
        
        return results
    
    def _prepare_inputs(self, texts: Union[str, List[str]], task_type: str = "general") -> Dict[str, torch.Tensor]:
        """
        Prepare inputs for model inference.
        
        Args:
            texts: Input text(s)
            task_type: Type of task ("mlm" or "embedding")
            
        Returns:
            Dictionary of prepared model inputs
        """
        if isinstance(texts, str):
            texts = [texts]
        
        # Validate inputs
        ValidationUtils.validate_texts(texts, max_length=self.inference_config.max_sequence_length * 4)
        
        # Prepare inputs based on task type
        if task_type == "mlm":
            inputs = self.input_processor.prepare_mlm_inputs(texts)
        else:
            inputs = self.input_processor.prepare_embedding_inputs(texts)
        
        # Validate prepared inputs
        ValidationUtils.validate_model_inputs(inputs, self.model_config.is_unpadded)
        
        return inputs
    
    def _run_model(self, model_inputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Run model inference with prepared inputs.
        
        Args:
            model_inputs: Prepared model inputs
            
        Returns:
            Model outputs
        """
        if self.model is None:
            raise RuntimeError("Model not initialized")
        
        # Set model to eval mode and disable gradients
        self.model.eval()
        
        with torch.no_grad():
            # Handle precision context
            if self.inference_config.is_mixed_precision:
                if self.inference_config.precision == "fp16":
                    with torch.cuda.amp.autocast(dtype=torch.float16):
                        outputs = self.model(**model_inputs)
                elif self.inference_config.precision == "bf16":
                    with torch.cuda.amp.autocast(dtype=torch.bfloat16):
                        outputs = self.model(**model_inputs)
                else:
                    outputs = self.model(**model_inputs)
            else:
                outputs = self.model(**model_inputs)
        
        return outputs
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        if self.model_factory is None:
            return {"status": "not_initialized"}
        
        info = self.model_factory.get_model_info()
        info.update({
            "pipeline_type": self.__class__.__name__,
            "checkpoint_path": str(self.checkpoint_path) if self.checkpoint_path else None,
            "config_info": self.model_config.to_dict(),
            "inference_config": self.inference_config.to_dict(),
        })
        
        return info
    
    def get_tokenizer_info(self) -> Dict[str, Any]:
        """
        Get information about the tokenizer.
        
        Returns:
            Dictionary with tokenizer information
        """
        if self.input_processor is None:
            return {"status": "not_initialized"}
        
        return self.input_processor.get_tokenizer_info()
    
    def validate_configuration(self) -> bool:
        """
        Validate the pipeline configuration.
        
        Returns:
            True if configuration is valid
        """
        try:
            # Validate model config compatibility with checkpoint
            if self.checkpoint_path and self.model_factory:
                if not self.model_factory.validate_model_config_compatibility(self.checkpoint_path):
                    logger.error("Model configuration incompatible with checkpoint")
                    return False
            
            # Validate inference configuration
            if self.inference_config.max_sequence_length > self.model_config.max_position_embeddings:
                logger.warning(
                    f"Inference max_sequence_length ({self.inference_config.max_sequence_length}) "
                    f"exceeds model max_position_embeddings ({self.model_config.max_position_embeddings})"
                )
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'model') and self.model is not None:
            del self.model
            self.model = None
        
        # Clear CUDA cache if using GPU
        if self.inference_config.is_cuda:
            torch.cuda.empty_cache()
        
        logger.info(f"{self.__class__.__name__} resources cleaned up")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup() 