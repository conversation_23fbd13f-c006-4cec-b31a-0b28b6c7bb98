# ModernBERT Inference Environment Setup

This guide will help you create a clean, conflict-free conda environment specifically for running ModernBERT inference.

## 🚀 Quick Setup

### Option 1: GPU Environment (Recommended)

```bash
# Create the environment
conda env create -f inference/modernbert-inference-environment.yaml

# Activate the environment
conda activate modernbert-inference

# Verify installation
python inference/environment_test.py
```

### Option 2: CPU-Only Environment

```bash
# Create CPU-only environment
conda env create -f inference/modernbert-inference-cpu.yaml

# Activate the environment
conda activate modernbert-inference-cpu

# Verify installation
python inference/environment_test.py
```

## 📋 What's Included

### Core Dependencies
- **Python 3.11**: Latest stable Python version
- **PyTorch 2.1.0**: Core deep learning framework
- **CUDA 12.1**: GPU acceleration (GPU version only)
- **Flash Attention 2.3.3**: Efficient attention implementation

### Essential Libraries
- **Transformers 4.35.2**: For tokenizers (minimal install)
- **OmegaConf 2.3.0**: Configuration management
- **NumPy/SciPy**: Scientific computing
- **Jupyter**: Notebook support

### Optional Tools
- **Sentence Transformers**: For similarity comparisons
- **Pandas/Matplotlib**: Data analysis and visualization
- **scikit-learn**: Machine learning utilities

## 🔧 Environment Details

### GPU Environment
- **Name**: `modernbert-inference`
- **CUDA**: 12.1 compatible
- **Memory**: Optimized for inference workloads
- **Size**: ~3-4 GB

### CPU Environment  
- **Name**: `modernbert-inference-cpu`
- **Hardware**: CPU-only execution
- **Memory**: Lower memory footprint
- **Size**: ~2-3 GB

## 🧪 Testing Your Installation

### 1. Environment Test
```bash
conda activate modernbert-inference
python inference/environment_test.py
```

### 2. Config Loading Test
```bash
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from inference.config.model_config import ModelConfig
config = ModelConfig('yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml')
print(f'✅ Config loaded: {config.hidden_size}D, {config.padding} padding')
"
```

### 3. Full Inference Test
```bash
# Update paths in the script first
python inference/working_example.py
```

## 🐛 Troubleshooting

### Common Issues

#### "Environment solving failed"
```bash
# Clear conda cache and try again
conda clean --all
conda env create -f inference/modernbert-inference-environment.yaml
```

#### "CUDA not available"
```bash
# Use CPU environment instead
conda env create -f inference/modernbert-inference-cpu.yaml
conda activate modernbert-inference-cpu
```

#### "Flash attention installation failed"
```bash
# Skip flash attention (will use standard attention)
conda activate modernbert-inference
pip uninstall flash-attn
```

### Verification Commands
```bash
# Check PyTorch installation
python -c "import torch; print(f'PyTorch: {torch.__version__}')"

# Check CUDA availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# Check transformers
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
```

## 📱 Jupyter Notebook Usage

### 1. Start Jupyter in the Environment
```bash
conda activate modernbert-inference
jupyter notebook
```

### 2. Verify Kernel
In your notebook, check that you're using the correct kernel:
```python
import sys
print(f"Python executable: {sys.executable}")
print(f"Should contain: modernbert-inference")
```

### 3. Test Inference
```python
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

# Import the inference class
from inference.inference import ModernBERTInference

# Test with your checkpoint
config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
checkpoint_path = "your/checkpoint/path.pt"

with ModernBERTInference(config_path, checkpoint_path) as inference:
    # Test MLM
    predictions = inference.predict_masked_tokens("The capital of France is [MASK].")
    print("MLM:", predictions)
    
    # Test embeddings
    embeddings = inference.encode_texts(["Test sentence"])
    print("Embeddings shape:", embeddings.shape)
```

## 🔄 Environment Management

### Update Environment
```bash
# Update from environment file
conda env update -f inference/modernbert-inference-environment.yaml --prune
```

### Remove Environment
```bash
conda env remove -n modernbert-inference
```

### Export Your Environment
```bash
conda activate modernbert-inference
conda env export > my-inference-env.yaml
```

## 📊 Performance Tips

### GPU Optimization
- Use `device="auto"` for automatic GPU detection
- Set `precision="fp16"` for faster inference on modern GPUs
- Increase `max_batch_size` based on your GPU memory

### CPU Optimization
- Use `device="cpu"` explicitly
- Reduce `max_batch_size` to avoid memory issues
- Consider using `precision="fp32"` for stability

### Memory Management
```python
# For large datasets
inference_config = {
    "max_batch_size": 8,  # Reduce if out of memory
    "max_sequence_length": 512,  # Adjust based on your texts
}

with ModernBERTInference(config_path, checkpoint_path, **inference_config) as inference:
    # Your inference code
    pass
```

## 🆘 Support

If you encounter issues:

1. **Check this guide**: Most common issues are covered above
2. **Test step by step**: Use the verification commands
3. **Environment logs**: Check `conda env create` output for errors
4. **Fallback to CPU**: Try the CPU environment if GPU issues persist

## 🎯 Next Steps

Once your environment is working:

1. ✅ Update checkpoint paths in example scripts
2. ✅ Run `inference/working_example.py` to verify everything works
3. ✅ Try the Jupyter notebook examples
4. ✅ Explore the API documentation in `inference/README.md` 