#!/usr/bin/env python3
"""
Environment test script for ModernBERT inference.

This script verifies that the conda environment is properly set up
and all dependencies are working correctly.
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """Test Python version."""
    print("🐍 Testing Python version...")
    python_version = sys.version_info
    if python_version.major == 3 and python_version.minor >= 11:
        print(f"   ✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        return True
    else:
        print(f"   ❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} (need 3.11+)")
        return False

def test_torch():
    """Test PyTorch installation."""
    print("\n🔥 Testing PyTorch...")
    try:
        import torch
        print(f"   ✅ PyTorch {torch.__version__}")
        
        # Test CUDA availability
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print(f"   ✅ CUDA available: {device_count} device(s)")
            print(f"   ✅ Primary GPU: {device_name}")
        else:
            print(f"   ⚠️  CUDA not available (CPU-only mode)")
        
        # Test basic tensor operations
        x = torch.randn(2, 3)
        y = torch.randn(3, 2)
        z = torch.mm(x, y)
        print(f"   ✅ Tensor operations working")
        
        return True
    except ImportError as e:
        print(f"   ❌ PyTorch import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ PyTorch test failed: {e}")
        return False

def test_transformers():
    """Test transformers library."""
    print("\n🤗 Testing Transformers...")
    try:
        import transformers
        print(f"   ✅ Transformers {transformers.__version__}")
        
        # Test tokenizer loading (lightweight test)
        from transformers import AutoTokenizer
        print(f"   ✅ AutoTokenizer available")
        
        return True
    except ImportError as e:
        print(f"   ❌ Transformers import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Transformers test failed: {e}")
        return False

def test_omegaconf():
    """Test OmegaConf."""
    print("\n⚙️  Testing OmegaConf...")
    try:
        from omegaconf import OmegaConf
        
        # Test basic functionality
        config = OmegaConf.create({"test": "value", "nested": {"key": 123}})
        assert config.test == "value"
        assert config.nested.key == 123
        
        print(f"   ✅ OmegaConf working")
        return True
    except ImportError as e:
        print(f"   ❌ OmegaConf import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ OmegaConf test failed: {e}")
        return False

def test_essential_libraries():
    """Test other essential libraries."""
    print("\n📚 Testing Essential Libraries...")
    
    libraries = [
        ("numpy", "NumPy"),
        ("scipy", "SciPy"),
        ("pathlib", "pathlib"),
        ("logging", "logging"),
    ]
    
    success = True
    for module_name, display_name in libraries:
        try:
            __import__(module_name)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ❌ {display_name}")
            success = False
    
    return success

def test_optional_libraries():
    """Test optional libraries."""
    print("\n🎁 Testing Optional Libraries...")
    
    optional_libraries = [
        ("pandas", "Pandas"),
        ("matplotlib", "Matplotlib"),
        ("seaborn", "Seaborn"),
        ("sklearn", "scikit-learn"),
        ("tqdm", "tqdm"),
        ("rich", "Rich"),
    ]
    
    available = []
    for module_name, display_name in optional_libraries:
        try:
            __import__(module_name)
            print(f"   ✅ {display_name}")
            available.append(display_name)
        except ImportError:
            print(f"   ⚠️  {display_name} (optional)")
    
    print(f"   📊 {len(available)}/{len(optional_libraries)} optional libraries available")
    return True

def test_flash_attention():
    """Test Flash Attention (optional)."""
    print("\n⚡ Testing Flash Attention...")
    try:
        import flash_attn
        print(f"   ✅ Flash Attention available")
        return True
    except ImportError:
        print(f"   ⚠️  Flash Attention not available (will use standard attention)")
        return True
    except Exception as e:
        print(f"   ⚠️  Flash Attention test failed: {e}")
        return True

def test_inference_imports():
    """Test ModernBERT inference imports."""
    print("\n🔍 Testing ModernBERT Inference Imports...")
    
    # Add project root to path
    sys.path.insert(0, str(Path.cwd()))
    
    try:
        # Test config imports
        from inference.config.model_config import ModelConfig
        from inference.config.inference_config import InferenceConfig
        print(f"   ✅ Config classes imported")
        
        # Test if we can create basic config objects
        inference_config = InferenceConfig()
        print(f"   ✅ InferenceConfig created: device={inference_config.device}")
        
        # Test main inference class import
        from inference.inference import ModernBERTInference
        print(f"   ✅ ModernBERTInference imported")
        
        return True
    except ImportError as e:
        print(f"   ❌ Inference import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Inference test failed: {e}")
        return False

def test_config_loading():
    """Test config file loading."""
    print("\n📄 Testing Config Loading...")
    
    sys.path.insert(0, str(Path.cwd()))
    
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    
    if not os.path.exists(config_path):
        print(f"   ⚠️  Config file not found: {config_path}")
        print(f"   ℹ️  This is expected if you haven't updated the path")
        return True
    
    try:
        from inference.config.model_config import ModelConfig
        model_config = ModelConfig(config_path)
        
        print(f"   ✅ Config loaded successfully")
        print(f"   📊 Model info:")
        print(f"      - Hidden size: {model_config.hidden_size}")
        print(f"      - Layers: {model_config.num_hidden_layers}")
        print(f"      - Padding: {model_config.padding}")
        print(f"      - Unpadded: {model_config.is_unpadded}")
        print(f"      - Tokenizer: {model_config.tokenizer_name}")
        
        return True
    except Exception as e:
        print(f"   ❌ Config loading failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("🧪 ModernBERT Inference Environment Test")
    print("=" * 60)
    
    # Check environment name
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"📦 Conda environment: {conda_env}")
    if 'modernbert-inference' in conda_env.lower():
        print("   ✅ Running in ModernBERT inference environment")
    else:
        print("   ⚠️  Not running in expected environment")
    
    print(f"📍 Working directory: {os.getcwd()}")
    print(f"🐍 Python executable: {sys.executable}")
    
    # Run tests
    tests = [
        test_python_version,
        test_torch,
        test_transformers,
        test_omegaconf,
        test_essential_libraries,
        test_optional_libraries,
        test_flash_attention,
        test_inference_imports,
        test_config_loading,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   💥 Test crashed: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All tests passed! Your environment is ready for ModernBERT inference.")
        print("\n🚀 Next steps:")
        print("1. Update checkpoint paths in example scripts")
        print("2. Run: python inference/working_example.py")
        print("3. Start Jupyter: jupyter notebook")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you activated the correct environment")
        print("2. Try reinstalling failed packages")
        print("3. Check the SETUP.md guide for solutions")
    
    print(f"\n📚 For more help, see: inference/SETUP.md")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 