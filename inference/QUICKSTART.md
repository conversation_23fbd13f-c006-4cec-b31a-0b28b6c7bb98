# 🚀 ModernBERT Inference Quick Start

Get up and running with ModernBERT inference in 5 minutes!

## Step 1: Create Clean Environment

### For GPU Users (Recommended)
```bash
conda env create -f inference/modernbert-inference-environment.yaml
conda activate modernbert-inference
```

### For CPU Users
```bash
conda env create -f inference/modernbert-inference-cpu.yaml
conda activate modernbert-inference-cpu
```

## Step 2: Test Environment
```bash
python inference/environment_test.py
```

You should see:
```
🎉 All tests passed! Your environment is ready for ModernBERT inference.
```

## Step 3: Update Your Paths

Edit these variables with your actual paths:
```python
config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
checkpoint_path = "your/actual/checkpoint/path.pt"
```

## Step 4: Test Inference

### In Python Script
```bash
python inference/working_example.py
```

### In Jupyter Notebook
```python
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

# Import the inference class
from inference.inference import ModernBERTInference

# Your paths
config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"

# Use the inference system
with ModernBERTInference(config_path, checkpoint_path) as inference:
    # MLM inference
    predictions = inference.predict_masked_tokens("The capital of France is [MASK].")
    print("Predictions:", predictions)
    
    # Embedding generation  
    embeddings = inference.encode_texts(["This is a sentence.", "Another sentence."])
    print("Embeddings shape:", embeddings.shape)
```

## What You Get

✅ **Auto-Configuration**: Automatically detects your model's unpadded attention setup  
✅ **MLM Inference**: Mask-fill predictions with top-k sampling  
✅ **Embeddings**: Sentence embeddings with multiple pooling strategies  
✅ **Clean Environment**: No dependency conflicts  
✅ **Memory Efficient**: Optimized for inference workloads  

## If You Have Issues

1. **Environment creation fails**: See `inference/SETUP.md` for troubleshooting
2. **Import errors**: Make sure you're in the right conda environment
3. **Checkpoint loading fails**: Verify your checkpoint path is correct
4. **GPU issues**: Try the CPU environment or set `device="cpu"`

## Next Steps

- 📖 Read `inference/README.md` for full API documentation
- 🧪 Try `inference/examples/` for more advanced usage
- ⚙️ See `inference/SETUP.md` for detailed configuration options

Happy inferencing! 🎉 