#!/usr/bin/env python3
"""
Debug script to understand MLM prediction issues.
"""

import sys
from pathlib import Path
import torch

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

from inference import ModernBERTInference

def debug_mlm_prediction():
    """Debug MLM prediction step by step."""
    
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
    
    print("🔍 Debugging MLM Prediction")
    print("=" * 50)
    
    with ModernBERTInference(config_path, checkpoint_path) as inference:
        # Get the MLM pipeline
        mlm_pipeline = inference._get_mlm_pipeline()
        
        # Test multiple texts
        test_texts = [
            "The capital of France is <mask>.",
            "My name is <mask>.",
            "The weather today is <mask>.",
            "I like to eat <mask>."
        ]

        for text in test_texts:
            print(f"\n{'='*60}")
            print(f"Input text: {text}")

            # Quick test with pipeline
            predictions = mlm_pipeline.predict([text], top_k=5)
            print("Top 5 pipeline predictions:")
            if predictions and 'mask_predictions' in predictions:
                for mask_pred in predictions['mask_predictions']:
                    for i, pred in enumerate(mask_pred['predictions'][:5]):
                        print(f"  {i+1}. '{pred['token']}' (prob: {pred['probability']:.3f})")

        # Detailed analysis for the first text
        text = test_texts[0]
        print(f"\n{'='*60}")
        print(f"DETAILED ANALYSIS FOR: {text}")
        
        # Check tokenization
        tokenizer = mlm_pipeline.input_processor.tokenizer
        tokens = tokenizer.tokenize(text)
        token_ids = tokenizer.encode(text)
        print(f"Tokens: {tokens}")
        print(f"Token IDs: {token_ids}")
        print(f"Mask token: {tokenizer.mask_token}")
        print(f"Mask token ID: {tokenizer.mask_token_id}")
        
        # Find mask positions
        mask_positions = [i for i, token_id in enumerate(token_ids) if token_id == tokenizer.mask_token_id]
        print(f"Mask positions in sequence: {mask_positions}")
        
        # Prepare inputs
        model_inputs = mlm_pipeline._prepare_inputs([text], task_type="mlm")
        print(f"Model inputs keys: {model_inputs.keys()}")
        print(f"Input IDs shape: {model_inputs['input_ids'].shape}")
        
        if mlm_pipeline.model_config.is_unpadded:
            print(f"Unpadded input IDs shape: {model_inputs['input_ids'].shape}")
            print(f"cu_seqlens: {model_inputs['cu_seqlens']}")
            print(f"max_seqlen: {model_inputs['max_seqlen']}")
            
            # Find mask in unpadded sequence
            unpadded_mask_positions = (model_inputs['input_ids'] == tokenizer.mask_token_id).nonzero(as_tuple=False)
            print(f"Mask positions in unpadded sequence: {unpadded_mask_positions}")
        
        # Run model
        print("\nRunning model...")
        model_outputs = mlm_pipeline._run_model(model_inputs)
        logits = model_outputs.logits
        print(f"Logits shape: {logits.shape}")
        
        # Check logits at mask position
        if mlm_pipeline.model_config.is_unpadded:
            mask_indices = (model_inputs['input_ids'] == tokenizer.mask_token_id).nonzero(as_tuple=False).flatten()
            if len(mask_indices) > 0:
                mask_idx = mask_indices[0].item()
                print(f"Checking logits at unpadded position {mask_idx}")
                mask_logits = logits[mask_idx]
                
                # Get top predictions
                top_logits, top_indices = torch.topk(mask_logits, 50)
                print(f"Top 10 logits: {top_logits[:10]}")
                print(f"Top 10 token IDs: {top_indices[:10]}")

                # Check if Paris is in top predictions
                paris_token_id = 5690
                paris_logit = mask_logits[paris_token_id].item()
                paris_rank = (mask_logits > paris_logit).sum().item() + 1
                print(f"Paris token ID {paris_token_id} logit: {paris_logit:.3f}, rank: {paris_rank}")

                # Decode top tokens
                print("Top 20 predictions:")
                for i, (logit, token_id) in enumerate(zip(top_logits[:20], top_indices[:20])):
                    token_text = tokenizer.decode([token_id.item()], skip_special_tokens=True)
                    prob = torch.softmax(mask_logits, dim=-1)[token_id].item()
                    print(f"  {i+1}. '{token_text}' (ID: {token_id.item()}, logit: {logit:.3f}, prob: {prob:.3f})")
        
        # Compare with pipeline prediction
        print("\nPipeline prediction:")
        predictions = mlm_pipeline.predict([text], top_k=5)
        print(f"Pipeline result: {predictions}")

if __name__ == "__main__":
    debug_mlm_prediction()
