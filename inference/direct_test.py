#!/usr/bin/env python3
# Direct test that bypasses main package imports

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("Testing direct imports (bypassing main package)...")

try:
    # Import config classes directly
    print("1. Testing direct config imports...")
    
    # Import the modules directly, not through the package
    import inference.config.inference_config as inf_config_module
    import inference.config.model_config as model_config_module
    
    print("   ✓ Config modules imported successfully")
    
    # Test creating config objects
    inference_config = inf_config_module.InferenceConfig()
    print(f"   ✓ InferenceConfig created - device: {inference_config.device}")
    
    # Test loading model config
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    if os.path.exists(config_path):
        print(f"2. Testing model config loading from: {config_path}")
        model_config = model_config_module.ModelConfig(config_path)
        print(f"   ✓ Config loaded - Hidden size: {model_config.hidden_size}")
        print(f"   ✓ Padding: {model_config.padding}")
        print(f"   ✓ Is unpadded: {model_config.is_unpadded}")
        
        # Now test creating the main inference class directly
        print("3. Testing main inference import...")
        import inference.inference as inference_module
        ModernBERTInference = inference_module.ModernBERTInference
        print("   ✓ ModernBERTInference imported successfully")
        
        print("\n🎉 Direct imports successful!")
        print("✅ Ready to test with your checkpoint!")
        
    else:
        print(f"   ⚠ Config file not found: {config_path}")
        print("   Update the path and try again")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n📝 For Jupyter notebook usage:")
print("Use: from inference.inference import ModernBERTInference")
print("Or:  import inference.inference as inf; inf.ModernBERTInference(...)") 