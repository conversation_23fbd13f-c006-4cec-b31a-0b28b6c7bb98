#!/usr/bin/env python3
# Quick test to check if inference module imports work

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("Testing core inference imports...")

try:
    print("1. Testing basic torch import...")
    import torch
    print("   ✓ PyTorch imported successfully")
    
    print("2. Testing config imports...")
    from inference.config.model_config import ModelConfig
    from inference.config.inference_config import InferenceConfig
    print("   ✓ Config classes imported successfully")
    
    print("3. Testing inference main class...")
    from inference.inference import ModernBERTInference
    print("   ✓ ModernBERTInference imported successfully")
    
    print("\n🎉 All core imports successful!")
    print("The import issue has been resolved.")
    
    # Test with actual config file
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    if os.path.exists(config_path):
        print(f"\n4. Testing config loading from: {config_path}")
        model_config = ModelConfig(config_path)
        print(f"   ✓ Config loaded - Hidden size: {model_config.hidden_size}")
        print(f"   ✓ Padding detected: {model_config.padding}")
        print(f"   ✓ Is unpadded: {model_config.is_unpadded}")
    
    print("\n✅ Ready to test with actual checkpoint!")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
    if "transformers" in str(e) or "ssl" in str(e):
        print("This is likely an environment dependency issue, not our code.")
        print("Try: conda update urllib3 transformers")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)

print("\nNext step: Update checkpoint path in playground.ipynb and test!") 