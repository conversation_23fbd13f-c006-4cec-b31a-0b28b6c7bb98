# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union
from omegaconf import OmegaConf, DictConfig
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

logger = logging.getLogger(__name__)


class ModelConfig:
    """
    Loads and validates model configuration from YAML training configs.
    
    Automatically detects padding/unpadding configuration and other model
    architecture parameters needed for inference.
    """
    
    def __init__(self, config_path: str):
        """
        Initialize ModelConfig from training YAML file.
        
        Args:
            config_path: Path to the training YAML configuration file
        """
        self.config_path = Path(config_path)
        if not self.config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        self._load_config()
        self._validate_config()
        self._extract_model_params()
        self._load_vocab_size()
    
    def _load_config(self):
        """Load the YAML configuration file."""
        try:
            # Load main config
            with open(self.config_path) as f:
                self.raw_config = OmegaConf.load(f)
            
            # Load defaults if they exist
            defaults_path = self.config_path.parent.parent / "defaults.yaml"
            if defaults_path.exists():
                with open(defaults_path) as f:
                    default_config = OmegaConf.load(f)
                # Merge with defaults as base
                self.config = OmegaConf.merge(default_config, self.raw_config)
            else:
                self.config = self.raw_config
                
            # Resolve variable substitutions
            OmegaConf.resolve(self.config)
            
        except Exception as e:
            raise ValueError(f"Failed to load config from {self.config_path}: {e}")
    
    def _validate_config(self):
        """Validate the loaded configuration."""
        if not hasattr(self.config, 'model'):
            raise ValueError("Config must contain 'model' section")
        
        if not hasattr(self.config.model, 'model_config'):
            raise ValueError("Config must contain 'model.model_config' section")
        
        # Validate required fields
        required_fields = ['tokenizer_name']
        for field in required_fields:
            if not hasattr(self.config, field) and not hasattr(self.config.model, field):
                raise ValueError(f"Config must contain '{field}' field")
    
    def _extract_model_params(self):
        """Extract and store key model parameters."""
        model_config = self.config.model.model_config
        
        # Core architecture parameters
        self.hidden_size = getattr(model_config, 'hidden_size', 768)
        self.num_hidden_layers = getattr(model_config, 'num_hidden_layers', 12)
        self.num_attention_heads = getattr(model_config, 'num_attention_heads', 12)
        self.intermediate_size = getattr(model_config, 'intermediate_size', 3072)
        self.max_position_embeddings = getattr(model_config, 'max_position_embeddings', 512)
        
        # Attention configuration
        self.attention_layer = getattr(model_config, 'attention_layer', 'base')
        self.bert_layer = getattr(model_config, 'bert_layer', 'prenorm')
        self.embedding_layer = getattr(model_config, 'embedding_layer', 'absolute_pos')
        self.mlp_layer = getattr(model_config, 'mlp_layer', 'glu')
        
        # Padding/unpadding configuration - KEY for inference behavior
        self.padding = getattr(model_config, 'padding', 'padded')
        self.unpad_embeddings = getattr(model_config, 'unpad_embeddings', False)
        self.pad_logits = getattr(model_config, 'pad_logits', True)
        
        # Attention-specific parameters
        self.attention_probs_dropout_prob = getattr(model_config, 'attention_probs_dropout_prob', 0.1)
        self.attn_out_dropout_prob = getattr(model_config, 'attn_out_dropout_prob', 0.1)
        self.attn_out_bias = getattr(model_config, 'attn_out_bias', False)
        self.attn_qkv_bias = getattr(model_config, 'attn_qkv_bias', False)
        
        # Sliding window and global attention (for ModernBERT)
        self.sliding_window = getattr(model_config, 'sliding_window', -1)
        self.global_attn_every_n_layers = getattr(model_config, 'global_attn_every_n_layers', -1)
        self.rotary_emb_base = getattr(model_config, 'rotary_emb_base', 10000.0)
        self.local_attn_rotary_emb_base = getattr(model_config, 'local_attn_rotary_emb_base', 10000.0)
        
        # Normalization and bias settings
        self.final_norm = getattr(model_config, 'final_norm', True)
        self.embed_norm = getattr(model_config, 'embed_norm', False)
        self.decoder_bias = getattr(model_config, 'decoder_bias', True)
        
        # Loss and output configuration
        self.tie_word_embeddings = getattr(model_config, 'tie_word_embeddings', True)
        
        # Note: vocab_size will be determined from tokenizer in _load_vocab_size()
        
        # Tokenizer configuration
        self.tokenizer_name = self.config.get('tokenizer_name', 
                                             self.config.model.get('tokenizer_name', 'bert-base-uncased'))
        
        # MLM probability for validation
        self.mlm_probability = getattr(self.config, 'mlm_probability', 0.15)
        
        logger.info(f"Loaded model config: padding={self.padding}, unpad_embeddings={self.unpad_embeddings}")
    
    def _load_vocab_size(self):
        """Load vocabulary size from tokenizer."""
        try:
            from transformers import AutoTokenizer
            tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_name)
            self.vocab_size = len(tokenizer)
            logger.info(f"Detected vocabulary size from tokenizer: {self.vocab_size}")
        except Exception as e:
            logger.warning(f"Failed to load tokenizer {self.tokenizer_name}: {e}")
            # Fallback to config or default
            model_config = self.config.model.model_config
            self.vocab_size = getattr(model_config, 'vocab_size', 30522)
            logger.warning(f"Using fallback vocabulary size: {self.vocab_size}")
    
    @property
    def is_unpadded(self) -> bool:
        """Check if model uses unpadded attention."""
        return self.padding == 'unpadded' or self.unpad_embeddings
    
    @property
    def model_name(self) -> str:
        """Get the model name from config."""
        return self.config.model.get('name', 'flex_bert')
    
    @property
    def pretrained_model_name(self) -> str:
        """Get the pretrained model name."""
        return self.config.model.get('pretrained_model_name', self.tokenizer_name)
    
    def get_model_config_dict(self) -> Dict[str, Any]:
        """
        Get model configuration as a dictionary suitable for FlexBertConfig.
        
        Returns:
            Dictionary containing all model configuration parameters
        """
        return {
            'hidden_size': self.hidden_size,
            'num_hidden_layers': self.num_hidden_layers,
            'num_attention_heads': self.num_attention_heads,
            'intermediate_size': self.intermediate_size,
            'max_position_embeddings': self.max_position_embeddings,
            'attention_layer': self.attention_layer,
            'bert_layer': self.bert_layer,
            'mlp_layer': self.mlp_layer,
            'embedding_layer': self.embedding_layer,
            'padding': self.padding,
            'unpad_embeddings': self.unpad_embeddings,
            'pad_logits': self.pad_logits,
            'attention_probs_dropout_prob': self.attention_probs_dropout_prob,
            'attn_out_dropout_prob': self.attn_out_dropout_prob,
            'attn_out_bias': self.attn_out_bias,
            'attn_qkv_bias': self.attn_qkv_bias,
            'sliding_window': self.sliding_window,
            'global_attn_every_n_layers': self.global_attn_every_n_layers,
            'rotary_emb_base': self.rotary_emb_base,
            'local_attn_rotary_emb_base': self.local_attn_rotary_emb_base,
            'final_norm': self.final_norm,
            'embed_norm': self.embed_norm,
            'decoder_bias': self.decoder_bias,
            'tie_word_embeddings': self.tie_word_embeddings,
            'vocab_size': self.vocab_size,
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'config_path': str(self.config_path),
            'model_name': self.model_name,
            'tokenizer_name': self.tokenizer_name,
            'is_unpadded': self.is_unpadded,
            'mlm_probability': self.mlm_probability,
            **self.get_model_config_dict()
        }
