#!/usr/bin/env python3
"""
Simple MLM (Masked Language Model) inference script for ModernBERT.

This script performs fill-mask prediction on input text containing [MASK] tokens.
It loads a trained model from a checkpoint file and its corresponding configuration.

Usage:
    python mlm_inference.py

Or modify the INPUT_TEXT variable below to test different inputs.
"""

import os
import sys
import torch
import yaml
from pathlib import Path
from typing import List, Dict, Any, Tuple
from omegaconf import OmegaConf
from transformers import AutoTokenizer

# Add src directory to path to import model classes
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from bert_layers.configuration_bert import FlexBertConfig
    from bert_layers.modeling_bert import FlexBertForMaskedLM
except ImportError as e:
    print(f"Error importing model classes: {e}")
    print("Make sure you're running this script from the repository root directory.")
    sys.exit(1)


# Configuration - modify these paths as needed
CONFIG_PATH = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
CHECKPOINT_PATH = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"

# Input text with [MASK] tokens - modify this to test different inputs
INPUT_TEXT = "The capital of France is [MASK]. It is a beautiful [MASK]."

# Inference settings
TOP_K = 5  # Number of top predictions to show for each mask
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"


def load_config(config_path: str) -> Dict[str, Any]:
    """Load and parse the training configuration file."""
    config_path = Path(config_path)

    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    print(f"Loading config from: {config_path}")

    # Load main config
    with open(config_path) as f:
        config = OmegaConf.load(f)

    # Load defaults if they exist
    defaults_path = config_path.parent.parent / "defaults.yaml"
    if defaults_path.exists():
        with open(defaults_path) as f:
            default_config = OmegaConf.load(f)
        # Merge with defaults as base
        config = OmegaConf.merge(default_config, config)

    # Resolve variable substitutions
    OmegaConf.resolve(config)

    return OmegaConf.to_container(config, resolve=True)


def create_model_config(config: Dict[str, Any]) -> FlexBertConfig:
    """Create FlexBertConfig from training configuration."""
    model_config = config["model"]["model_config"]

    # Get tokenizer name for pretrained model name
    tokenizer_name = config.get("tokenizer_name", "bert-base-uncased")

    # Create FlexBertConfig
    bert_config = FlexBertConfig.from_pretrained(
        tokenizer_name,
        **model_config
    )

    # Adjust vocab size for divisibility by 8 (as done in training)
    if bert_config.vocab_size % 8 != 0:
        bert_config.vocab_size += 8 - (bert_config.vocab_size % 8)

    return bert_config


def load_model_and_tokenizer(config_path: str, checkpoint_path: str) -> Tuple[FlexBertForMaskedLM, AutoTokenizer]:
    """Load the model and tokenizer from config and checkpoint."""

    # Load configuration
    config = load_config(config_path)

    # Create model configuration
    bert_config = create_model_config(config)

    # Load tokenizer
    tokenizer_name = config.get("tokenizer_name", "bert-base-uncased")
    print(f"Loading tokenizer: {tokenizer_name}")
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)

    # Create model
    print(f"Creating model with config: {bert_config}")
    model = FlexBertForMaskedLM(bert_config)

    # Load checkpoint
    checkpoint_path = Path(checkpoint_path)
    if not checkpoint_path.exists():
        raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

    print(f"Loading checkpoint from: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location="cpu")

    # Extract state dict from checkpoint (handle different checkpoint formats)
    if "state" in checkpoint and "model" in checkpoint["state"]:
        # Composer checkpoint format
        state_dict = checkpoint["state"]["model"]
    elif "model" in checkpoint:
        # Standard PyTorch format with model wrapper
        state_dict = checkpoint["model"]
    else:
        # Direct state dict
        state_dict = checkpoint

    # Load state dict into model
    model.load_state_dict(state_dict)

    # Move to device and set to eval mode
    model = model.to(DEVICE)
    model.eval()

    print(f"Model loaded successfully on {DEVICE}")
    return model, tokenizer


def predict_masked_tokens(model: FlexBertForMaskedLM, tokenizer: AutoTokenizer,
                         text: str, top_k: int = 5) -> List[Dict[str, Any]]:
    """Predict masked tokens in the input text."""

    print(f"\nInput text: {text}")

    # Tokenize input
    inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True)
    inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

    # Find mask token positions
    mask_token_id = tokenizer.mask_token_id
    mask_positions = (inputs["input_ids"] == mask_token_id).nonzero(as_tuple=True)

    if len(mask_positions[0]) == 0:
        print("No [MASK] tokens found in input text!")
        return []

    print(f"Found {len(mask_positions[0])} [MASK] token(s)")

    # Run model inference
    with torch.no_grad():
        outputs = model(**inputs)
        logits = outputs.logits

    # Process predictions for each mask
    predictions = []
    for i, (batch_idx, seq_pos) in enumerate(zip(mask_positions[0], mask_positions[1])):
        mask_logits = logits[batch_idx, seq_pos]

        # Get top-k predictions
        top_k_logits, top_k_indices = torch.topk(mask_logits, top_k)
        top_k_probs = torch.softmax(top_k_logits, dim=-1)

        # Convert to tokens and format results
        mask_predictions = []
        for j in range(top_k):
            token_id = top_k_indices[j].item()
            token = tokenizer.decode([token_id])
            probability = top_k_probs[j].item()

            mask_predictions.append({
                "token": token,
                "token_id": token_id,
                "probability": probability,
                "confidence": probability * 100
            })

        predictions.append({
            "mask_position": i + 1,
            "sequence_position": seq_pos.item(),
            "predictions": mask_predictions
        })

    return predictions


def main():
    """Main inference function."""
    print("=" * 60)
    print("ModernBERT MLM Inference Script")
    print("=" * 60)

    try:
        # Load model and tokenizer
        model, tokenizer = load_model_and_tokenizer(CONFIG_PATH, CHECKPOINT_PATH)

        # Run prediction
        predictions = predict_masked_tokens(model, tokenizer, INPUT_TEXT, TOP_K)

        # Display results
        print("\n" + "=" * 60)
        print("PREDICTION RESULTS")
        print("=" * 60)

        for mask_pred in predictions:
            print(f"\nMask #{mask_pred['mask_position']} (position {mask_pred['sequence_position']}):")
            print("-" * 40)

            for i, pred in enumerate(mask_pred["predictions"]):
                print(f"  {i+1:2d}. {pred['token']:15s} (confidence: {pred['confidence']:6.2f}%)")

        print("\n" + "=" * 60)
        print("Inference completed successfully!")

    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("\nPlease check that the config and checkpoint paths are correct:")
        print(f"  Config: {CONFIG_PATH}")
        print(f"  Checkpoint: {CHECKPOINT_PATH}")

    except Exception as e:
        print(f"Error during inference: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()